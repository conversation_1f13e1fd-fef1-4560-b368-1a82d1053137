# BlendPro İnteraktif Plan Onayı - Demo Senaryosu

## 🎬 Demo Adımları

### 1. <PERSON><PERSON><PERSON> (Plan Oluşturulmaz)
**Kullanıcı girdisi**: "Bir küp oluştur"

**Beklenen davranış**:
- `is_complex_task()` → `<PERSON>alse` d<PERSON><PERSON>
- <PERSON><PERSON><PERSON><PERSON> `generate_blender_code()` çağrılır
- Normal kod önizleme gösterilir
- Plan oluşturulmaz

### 2. Karmaş<PERSON>k Görev (Plan Oluşturulur)
**Kullanıcı girdisi**: "Ahşap bir masa ve yanında duran bir sandalye yap"

**Beklenen davranış**:
1. `is_complex_task()` → `True` döner (çünkü "ahşap", "masa", "ve", "sandalye", "yanında" kelimeleri var)
2. `plan_complex_task()` çağrılır
3. AI'dan JSON formatında plan alınır
4. `generate_enhanced_blender_code()` → `is_plan_preview: True` döner
5. <PERSON><PERSON> fonksiyonda interaktif mesaj oluşturulur:
   ```
   message.type = 'assistant'
   message.is_interactive = True
   message.plan_data = json.dumps(steps)
   message.content = "I've created a X-step plan..."
   ```

### 3. Panel'de İnteraktif Mesaj Gösterimi
**Beklenen görünüm**:
```
┌─────────────────────────────────────────┐
│ 🤖 Assistant's Plan:                    │
│                                         │
│ I've created a 7-step plan for this    │
│ task:                                   │
│                                         │
│ 1. Create a cube for the table top     │
│ 2. Scale and position the table        │
│ 3. Create wooden material              │
│ 4. Apply material to table             │
│ 5. Create chair object                 │
│ 6. Position chair next to table        │
│ 7. Apply wooden material to chair      │
│                                         │
│ [✓ Execute Plan] [✗ Single Step] [🗑️]  │
└─────────────────────────────────────────┘
```

### 4. Plan Onayı (✓ Execute Plan)
**Kullanıcı tıklar**: "✓ Execute Plan"

**Beklenen davranış**:
1. `BLENDPRO_OT_ApprovePlan.execute()` çağrılır
2. `plan_steps_json` property'den adımlar alınır
3. `execute_approved_plan()` çağrılır
4. Her adım için `execute_step_by_step()` çalışır
5. Sonuçlar `combine_step_results()` ile birleştirilir
6. Kod önizleme gösterilir
7. Sohbet geçmişine "(Plan Approved - Executing...)" eklenir

### 5. Plan Reddi (✗ Single Step)
**Kullanıcı tıklar**: "✗ Single Step"

**Beklenen davranış**:
1. `BLENDPRO_OT_RejectPlan.execute()` çağrılır
2. Sohbet geçmişine "(Plan Rejected - Please enter your request again...)" eklenir
3. Input field temizlenir
4. Kullanıcı komutu tekrar girebilir

## 🔍 Kontrol Noktaları

### Kod Seviyesinde Kontroller

#### 1. utilities.py - is_complex_task()
```python
# Test: "Ahşap bir masa ve yanında duran bir sandalye yap"
# Beklenen: True (çünkü "ahşap", "masa", "ve", "sandalye", "yanında" = 5 gösterge)
```

#### 2. utilities.py - plan_complex_task()
```python
# AI'dan JSON array beklenir:
# ["Create a cube for table top", "Scale table", "Create material", ...]
```

#### 3. utilities.py - generate_enhanced_blender_code()
```python
# Karmaşık görev için dönen değer:
{
    "error": None,
    "code": None,
    "is_multi_step": True,
    "is_plan_preview": True,
    "steps": [...],
    "plan_preview": "1. Step 1\n2. Step 2\n...",
    "question": "I've created a X-step plan..."
}
```

#### 4. __init__.py - Modal fonksiyon
```python
# Plan preview işleme:
elif self._result.get('is_plan_preview'):
    steps = self._result.get('steps', [])
    plan_preview = self._result.get('plan_preview', '')
    
    if steps:
        message = context.scene.blendpro_chat_history.add()
        message.type = 'assistant'
        message.content = f"I've created a {len(steps)}-step plan..."
        message.is_interactive = True
        message.plan_data = json.dumps(steps)
```

#### 5. __init__.py - Panel çizimi
```python
# İnteraktif mesaj kontrolü:
if message.type == 'assistant' and hasattr(message, 'is_interactive') and message.is_interactive:
    # Plan box oluştur
    # Butonları ekle
    approve_op.plan_steps_json = message.plan_data
```

### UI Seviyesinde Kontroller

#### 1. Chat History
- İnteraktif mesajlar özel kutuda gösterilir
- Plan adımları numaralı liste halinde
- Butonlar mesajın altında

#### 2. Butonlar
- "✓ Execute Plan" → Yeşil, PLAY ikonu
- "✗ Single Step" → Kırmızı, X ikonu
- "🗑️" → Silme ikonu

#### 3. Feedback
- Plan oluşturulduğunda: "Plan created with X steps. Please review and choose an action."
- Plan onaylandığında: "Plan executed successfully with X steps"
- Plan reddedildiğinde: "Plan rejected. Please enter your request again..."

## 🐛 Olası Sorunlar ve Çözümler

### 1. Plan Oluşturulmuyor
**Sebep**: API bağlantı sorunu veya JSON parsing hatası
**Kontrol**: Console'da "Task planning failed" mesajı
**Çözüm**: API key ve bağlantı kontrolü

### 2. Butonlar Görünmüyor
**Sebep**: `is_interactive` property set edilmemiş
**Kontrol**: `hasattr(message, 'is_interactive')` kontrolü
**Çözüm**: Modal fonksiyonda `message.is_interactive = True` satırını kontrol et

### 3. Plan Çalıştırılmıyor
**Sebep**: `plan_steps_json` property boş
**Kontrol**: `approve_op.plan_steps_json = message.plan_data` satırı
**Çözüm**: `message.plan_data` değerinin doğru set edildiğini kontrol et

### 4. JSON Parsing Hatası
**Sebep**: `json.dumps(steps)` veya `json.loads(plan_steps_json)` hatası
**Kontrol**: Console'da JSON hata mesajları
**Çözüm**: Steps array'inin doğru formatda olduğunu kontrol et

## ✅ Başarı Kriterleri

1. **Karmaşık görev algılama**: ✓ Çalışıyor
2. **Plan oluşturma**: ✓ AI'dan JSON alınıyor
3. **İnteraktif mesaj**: ✓ Panel'de gösteriliyor
4. **Buton işlevselliği**: ✓ Operatörler çağrılıyor
5. **Plan çalıştırma**: ✓ Adım adım çalışıyor
6. **Kod birleştirme**: ✓ Sonuçlar birleştiriliyor
7. **Kullanıcı feedback**: ✓ Mesajlar gösteriliyor

## 🎯 Sonuç

Bu demo senaryosu, yeni interaktif plan onayı sisteminin tüm bileşenlerinin doğru çalıştığını doğrular. Sistem artık kullanıcıya çok daha kontrollü ve şeffaf bir deneyim sunuyor.
