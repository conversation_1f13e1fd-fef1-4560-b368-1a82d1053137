#!/usr/bin/env python3
"""
Test script for the interactive plan approval system
This script simulates the workflow to verify the changes work correctly
"""

import json

# Test the is_complex_task function
def test_is_complex_task():
    """Test if complex task detection works"""
    
    # Import the function (this would normally be done in Blender)
    # from utilities import is_complex_task
    
    # Simulate the function logic
    def is_complex_task_simulation(prompt):
        complex_indicators = [
            'and', 've', 'ile', 'sonra', 'then', 'after',
            'oda', 'room', 'sahne', 'scene', 'ortam', 'environment',
            'masa', 'table', 'sandalye', 'chair', 'dolap', 'cabinet', 'yatak', 'bed',
            'ahşap', 'wood', 'metal', 'cam', 'glass', 'taş', 'stone',
            'içinde', 'inside', 'üzerinde', 'on top', 'yanında', 'next to',
            'büyük', 'küçük', 'yüksek', 'alçak', 'large', 'small', 'tall', 'short'
        ]
        
        prompt_lower = prompt.lower()
        indicator_count = sum(1 for indicator in complex_indicators if indicator in prompt_lower)
        
        return indicator_count >= 2
    
    # Test cases
    test_cases = [
        ("Bir küp oluştur", False),  # Simple task
        ("Ahşap bir masa ve yanında duran bir sandalye yap", True),  # Complex task
        ("Create a wooden table and a chair next to it", True),  # Complex task (English)
        ("Bir oda içinde masa ve sandalye oluştur", True),  # Complex task (Turkish)
        ("Make a large cube", False),  # Simple task
        ("Create a room with furniture inside", True),  # Complex task
    ]
    
    print("Testing is_complex_task function:")
    print("=" * 50)
    
    for prompt, expected in test_cases:
        result = is_complex_task_simulation(prompt)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{prompt}' -> {result} (expected: {expected})")
    
    print()

# Test the plan format function
def test_plan_formatting():
    """Test plan formatting"""
    
    def format_step_plan_for_user_simulation(steps):
        formatted_plan = []
        for i, step in enumerate(steps, 1):
            formatted_plan.append(f"{i}. {step}")
        return "\n".join(formatted_plan)
    
    # Sample steps
    sample_steps = [
        "Create a cube and scale it to room size",
        "Create a wooden material",
        "Apply wooden material to the cube",
        "Create a table object",
        "Position table inside the room",
        "Create a chair object",
        "Position chair next to the table"
    ]
    
    formatted = format_step_plan_for_user_simulation(sample_steps)
    
    print("Testing plan formatting:")
    print("=" * 50)
    print(formatted)
    print()

# Test the interactive message structure
def test_interactive_message():
    """Test interactive message structure"""
    
    sample_steps = [
        "Create a cube and scale it to room size",
        "Create a wooden material",
        "Apply wooden material to the cube",
        "Create a table object",
        "Position table inside the room"
    ]
    
    # Simulate the message structure that would be created
    message_data = {
        "type": "assistant",
        "content": f"I've created a {len(sample_steps)}-step plan for this task:\n\n" + 
                  "\n".join([f"{i}. {step}" for i, step in enumerate(sample_steps, 1)]),
        "is_interactive": True,
        "plan_data": json.dumps(sample_steps)
    }
    
    print("Testing interactive message structure:")
    print("=" * 50)
    print(f"Message type: {message_data['type']}")
    print(f"Is interactive: {message_data['is_interactive']}")
    print(f"Plan data (JSON): {message_data['plan_data']}")
    print(f"Content preview:\n{message_data['content'][:200]}...")
    print()

# Test the workflow
def test_workflow():
    """Test the complete workflow"""
    
    print("Testing complete workflow:")
    print("=" * 50)
    
    # Step 1: User enters complex command
    user_input = "Ahşap bir masa ve yanında duran bir sandalye yap"
    print(f"1. User input: '{user_input}'")
    
    # Step 2: System detects it's complex
    is_complex = True  # Would be determined by is_complex_task()
    print(f"2. Complex task detected: {is_complex}")
    
    # Step 3: System creates plan
    if is_complex:
        sample_plan = [
            "Create a cube for the table top",
            "Scale and position the table",
            "Create wooden material",
            "Apply material to table",
            "Create chair object",
            "Position chair next to table",
            "Apply wooden material to chair"
        ]
        print(f"3. Plan created with {len(sample_plan)} steps")
        
        # Step 4: Interactive message created
        print("4. Interactive message created with buttons:")
        print("   - ✓ Execute Plan")
        print("   - ✗ Single Step")
        
        # Step 5: User clicks button
        print("5. User clicks '✓ Execute Plan'")
        
        # Step 6: Plan executed
        print("6. Plan executed step by step")
        print("7. Code preview shown to user")
    
    print()

if __name__ == "__main__":
    print("BlendPro Interactive Plan Approval System Test")
    print("=" * 60)
    print()
    
    test_is_complex_task()
    test_plan_formatting()
    test_interactive_message()
    test_workflow()
    
    print("Test completed! The interactive plan approval system should work as follows:")
    print("1. User enters complex command")
    print("2. System detects complexity and creates plan")
    print("3. Interactive message appears with plan and buttons")
    print("4. User clicks button to approve or reject")
    print("5. System executes plan or falls back to single-step")
