import sys
import os
import bpy
import bpy.props
import re
import json

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# OpenAI client will be created when needed

from .utilities import *

# Import vision utilities with error handling
try:
    from .vision_utilities import *
    VISION_SYSTEM_AVAILABLE = True
    print("BlendPro Vision System loaded successfully")
except ImportError as e:
    print(f"Vision System not available: {e}")
    VISION_SYSTEM_AVAILABLE = False
except Exception as e:
    print(f"Error loading Vision System: {e}")
    VISION_SYSTEM_AVAILABLE = False

# Import scene monitoring system with error handling
try:
    from .scene_monitor import *
    SCENE_MONITOR_AVAILABLE = True
    print("BlendPro Scene Monitor loaded successfully")
except ImportError as e:
    print(f"Scene Monitor not available: {e}")
    SCENE_MONITOR_AVAILABLE = False
except Exception as e:
    print(f"Error loading Scene Monitor: {e}")
    SCENE_MONITOR_AVAILABLE = False
bl_info = {
    "name": "BlendPro - Advanced AI Blender Assistant",
    "blender": (2, 82, 0),
    "category": "Object",
    "author": "inkbytefo",
    "version": (3, 0, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Advanced AI-powered Blender assistant with code preview, auto-save, export/import, and undo features.",
    "warning": "",
    "wiki_url": "https://github.com/inkbytefo/BlendPro",
    "tracker_url": "https://github.com/inkbytefo/BlendPro/issues",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software.

IMPORTANT BEHAVIOR RULES:
1. If the user is asking a QUESTION about Blender, scene, or general information (like "What objects are in my scene?", "How do I do X?", "What is Y?"), respond with helpful text explanation, NOT code.
2. If the user is asking you to DO something or CREATE something in Blender, respond with Python code only.
3. For questions, be conversational and helpful. For tasks, provide only code.

CODE GENERATION RULES (when user wants you to DO something):
- Respond with your code in markdown (```).
- Preferably import entire modules instead of bits.
- Do not perform destructive operations on the meshes.
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

QUESTION RESPONSE RULES (when user is ASKING about something):
- Provide clear, helpful explanations
- You can mention code examples but don't make the entire response code
- Be conversational and informative

Examples:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```

user: What objects are in my scene?
assistant: I can see the objects currently in your scene. Let me list them for you based on the scene context provided. [Then provide helpful explanation about the objects]

user: How do I scale an object in Blender?
assistant: There are several ways to scale objects in Blender: [Then provide helpful explanation]"""



class BLENDPRO_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "blendpro.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.blendpro_chat_history.remove(self.message_index)
        return {'FINISHED'}

class BLENDPRO_OT_ShowCode(bpy.types.Operator):
    bl_idname = "blendpro.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "BLENDPRO_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)

        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class BLENDPRO_PT_Panel(bpy.types.Panel):
    bl_label = "BlendPro Blender Assistant"
    bl_idname = "BLENDPRO_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlendPro Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        column.label(text="Chat history:")
        box = column.box()
        for index, message in enumerate(context.scene.blendpro_chat_history):
            # Check if this is an interactive plan approval message
            if message.type == 'assistant' and hasattr(message, 'is_interactive') and message.is_interactive:
                # Create a special box for interactive plan messages
                plan_box = box.box()
                plan_box.label(text="🤖 Assistant's Plan:", icon="SETTINGS")

                # Show plan content with proper formatting
                plan_lines = message.content.split('\n')
                for line in plan_lines[:15]:  # Show first 15 lines to avoid UI overflow
                    if line.strip():
                        plan_box.label(text=line)

                if len(plan_lines) > 15:
                    plan_box.label(text="... (see console for full plan)")

                # Add interactive buttons directly in the message
                button_row = plan_box.row(align=True)

                # Approve button with plan data
                approve_op = button_row.operator("blendpro.approve_plan", text="✓ Execute Plan", icon="PLAY")
                if hasattr(message, 'plan_data') and message.plan_data:
                    approve_op.plan_steps_json = message.plan_data

                # Reject button
                reject_op = button_row.operator("blendpro.reject_plan", text="✗ Single Step", icon="X")

                # Delete button
                delete_message_op = button_row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

            elif message.type == 'assistant':
                # Regular assistant message
                row = box.row()
                row.label(text="Assistant: ")
                show_code_op = row.operator("blendpro.show_code", text="Show Code")
                show_code_op.code = message.content
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                # User message
                row = box.row()
                row.label(text=f"User: {message.content}")
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()



        # Get addon preferences to check if custom model is being used
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Base AI Model Selection
        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            column.label(text=f"Model: {addon_prefs.custom_model} (Custom)")
        else:
            column.label(text="GPT Model:")
            column.prop(context.scene, "blendpro_model", text="")

        # Vision Model Info (if vision system available)
        if VISION_SYSTEM_AVAILABLE:
            if addon_prefs.use_custom_vision_model and addon_prefs.custom_vision_model:
                column.label(text=f"Vision: {addon_prefs.custom_vision_model} (Custom)")
            else:
                vision_model = context.scene.blendpro_vision_model
                column.label(text=f"Vision: {vision_model}")



        column.label(text="Enter your message:")
        column.prop(context.scene, "blendpro_chat_input", text="")
        button_label = "Please wait...(this might take some time)" if context.scene.blendpro_button_pressed else "Execute"
        row = column.row(align=True)
        row.operator("blendpro.send_message", text=button_label)
        row.operator("blendpro.clear_chat", text="Clear Chat")

        # Export/Import buttons
        row = column.row(align=True)
        row.operator("blendpro.export_chat", text="Export Chat", icon="EXPORT")
        row.operator("blendpro.import_chat", text="Import Chat", icon="IMPORT")

        # Undo/Backup buttons
        row = column.row(align=True)
        row.operator("blendpro.undo_operation", text="Undo Last", icon="LOOP_BACK")
        row.operator("blendpro.show_backups", text="Show Backups", icon="FILE_BACKUP")
        row.operator("blendpro.cleanup_backups", text="Cleanup", icon="TRASH")

        column.separator()

        # AI Context Status (simplified)
        if VISION_SYSTEM_AVAILABLE or SCENE_MONITOR_AVAILABLE:
            box = column.box()
            box.label(text="AI Context System")

            # Context status indicators
            status_row = box.row()
            if VISION_SYSTEM_AVAILABLE:
                try:
                    from .vision_utilities import get_vision_system_status
                    vision_status = get_vision_system_status()
                    if vision_status.get("available", False):
                        status_row.label(text="Vision: Ready")
                    else:
                        status_row.label(text="Vision: Limited")
                except:
                    status_row.label(text="Vision: Unknown")

            if SCENE_MONITOR_AVAILABLE:
                if context.scene.blendpro_monitoring_active:
                    status_row.label(text="Monitor: Active")
                else:
                    status_row.label(text="Monitor: Inactive")

            # Simple monitoring toggle
            if SCENE_MONITOR_AVAILABLE:
                monitor_text = "Stop Monitoring" if context.scene.blendpro_monitoring_active else "Start Monitoring"
                box.operator("blendpro.toggle_scene_monitoring", text=monitor_text)



        column.separator()

class BLENDPRO_OT_ClearChat(bpy.types.Operator):
    bl_idname = "blendpro.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.blendpro_chat_history.clear()
        # Auto-save empty chat history
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)
        return {'FINISHED'}

class BLENDPRO_OT_ExportChat(bpy.types.Operator):
    bl_idname = "blendpro.export_chat"
    bl_label = "Export Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to export chat history",
        default="blendpro_chat_export.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json
            history_data = []

            for message in context.scene.blendpro_chat_history:
                history_data.append({
                    "type": message.type,
                    "content": message.content
                })

            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)

            self.report({'INFO'}, f"Chat history exported to: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error exporting chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_ImportChat(bpy.types.Operator):
    bl_idname = "blendpro.import_chat"
    bl_label = "Import Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to import chat history",
        default="",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json

            with open(self.filepath, 'r', encoding='utf-8') as f:
                history_data = json.load(f)

            # Clear existing history
            context.scene.blendpro_chat_history.clear()

            # Load imported history
            for item in history_data:
                message = context.scene.blendpro_chat_history.add()
                message.type = item.get("type", "user")
                message.content = item.get("content", "")

            # Auto-save imported history
            from .utilities import save_chat_history
            save_chat_history(context.scene.blendpro_chat_history)

            self.report({'INFO'}, f"Chat history imported from: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error importing chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_CodePreview(bpy.types.Operator):
    bl_idname = "blendpro.code_preview"
    bl_label = "Code Preview"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Generated Code",
        description="The generated Python code to preview",
        default=""
    )

    def execute(self, context):
        # Save scene state before executing code
        from .utilities import save_scene_state, cleanup_old_backups
        backup_path = save_scene_state()

        # Execute the code
        try:
            global_namespace = globals().copy()
            exec(self.code, global_namespace)
            self.report({'INFO'}, "Code executed successfully! (Backup saved for undo)")

            # Clean up old backups periodically
            cleanup_old_backups(10)

            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error executing code: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=600)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Generated Code Preview:", icon="FILE_SCRIPT")

        # Create a scrollable text area
        box = layout.box()
        lines = self.code.split('\n')

        # Show first 20 lines with line numbers
        for i, line in enumerate(lines[:20]):
            row = box.row()
            row.alignment = 'LEFT'
            row.label(text=f"{i+1:3d}: {line}")

        if len(lines) > 20:
            box.label(text=f"... and {len(lines) - 20} more lines")

        layout.separator()
        layout.label(text="Do you want to execute this code?", icon="QUESTION")

class BLENDPRO_OT_UndoOperation(bpy.types.Operator):
    bl_idname = "blendpro.undo_operation"
    bl_label = "Undo Last Operation"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .utilities import get_recent_backups
            backups = get_recent_backups(2)  # Get last 2 backups

            if len(backups) >= 2:
                # Load the previous backup (second most recent)
                backup_path = backups[1]
                bpy.ops.wm.open_mainfile(filepath=backup_path)
                self.report({'INFO'}, f"Undone to previous state: {os.path.basename(backup_path)}")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "No previous state available for undo")
                return {'CANCELLED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error during undo: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_ShowBackups(bpy.types.Operator):
    bl_idname = "blendpro.show_backups"
    bl_label = "Show Recent Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        from .utilities import get_recent_backups
        backups = get_recent_backups(10)

        if backups:
            self.report({'INFO'}, f"Found {len(backups)} recent backups")
            for i, backup in enumerate(backups):
                print(f"{i+1}. {os.path.basename(backup)} - {os.path.getmtime(backup)}")
        else:
            self.report({'INFO'}, "No backups found")

        return {'FINISHED'}

class BLENDPRO_OT_CleanupBackups(bpy.types.Operator):
    bl_idname = "blendpro.cleanup_backups"
    bl_label = "Cleanup Old Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            from .utilities import cleanup_old_backups
            cleanup_old_backups(5)  # Keep only 5 most recent
            self.report({'INFO'}, "Old backups cleaned up")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error cleaning backups: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_Execute(bpy.types.Operator):
    bl_idname = "blendpro.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    # Background processing variables
    _timer = None
    _thread = None
    _result = None
    _error = None
    _processing = False

    # Question-answer dialog variables
    _waiting_for_answer = False
    _current_question = None



    def execute(self, context):
        # Check if already processing
        if self._processing:
            self.report({'WARNING'}, "Already processing a request. Please wait...")
            return {'CANCELLED'}

        # Get preferences
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Get API key from addon preferences or environment
        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
            return {'CANCELLED'}

        # Get base URL from preferences or environment
        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        # Get model selection
        model = None
        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            model = addon_prefs.custom_model

        # Store user input and add to chat history
        user_input = context.scene.blendpro_chat_input
        if not user_input.strip():
            self.report({'ERROR'}, "Please enter a message.")
            return {'CANCELLED'}

        # Check if we're answering a previous question
        if self._waiting_for_answer and self._current_question:
            # Add context about the previous question to the user input
            contextual_input = f"Previous question: {self._current_question}\nMy answer: {user_input}"

            # Reset question state
            self._waiting_for_answer = False
            self._current_question = None

            # Use the contextual input for processing
            user_input = contextual_input



        message = context.scene.blendpro_chat_history.add()
        message.type = 'user'
        message.content = user_input

        # Auto-save chat history after adding user message
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)

        # Clear the chat input field
        context.scene.blendpro_chat_input = ""

        # Set processing state
        context.scene.blendpro_button_pressed = True
        self._processing = True
        self._result = None
        self._error = None

        # Start background processing
        import threading
        self._thread = threading.Thread(
            target=self._background_process,
            args=(user_input, context.scene.blendpro_chat_history, context, api_key, base_url, model)
        )
        self._thread.daemon = True
        self._thread.start()

        # Start timer to check for completion
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        return {'RUNNING_MODAL'}

    def _background_process(self, user_input, chat_history, context, api_key, base_url, model):
        """Background thread function for API call"""
        try:
            # Get addon preferences for AI configuration
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            # Use enhanced code generation with multi-step planning and question handling
            from .utilities import generate_enhanced_blender_code
            result = generate_enhanced_blender_code(
                user_input,
                chat_history,
                context,
                system_prompt,
                api_key,
                base_url,
                model,
                timeout=60,  # 60 second timeout
                temperature=addon_prefs.temperature,
                max_tokens=addon_prefs.max_tokens,
                top_p=addon_prefs.top_p
            )
            self._result = result
        except Exception as e:
            self._error = f"Background processing error: {str(e)}"

    def modal(self, context, event):
        """Modal handler to check background process completion"""
        if event.type == 'TIMER':
            # Check if thread is still alive
            if self._thread and self._thread.is_alive():
                return {'PASS_THROUGH'}

            # Thread completed, process results
            wm = context.window_manager
            wm.event_timer_remove(self._timer)
            self._timer = None

            context.scene.blendpro_button_pressed = False
            self._processing = False

            # Handle errors
            if self._error:
                self.report({'ERROR'}, self._error)
                return {'CANCELLED'}

            if not self._result:
                self.report({'ERROR'}, "No response received from API")
                return {'CANCELLED'}

            # Check for API errors
            if self._result.get('error'):
                error_msg = self._result['error']
                if 'timeout' in error_msg.lower():
                    self.report({'ERROR'}, "Request timed out. Please try again.")
                elif 'rate limit' in error_msg.lower():
                    self.report({'ERROR'}, "API rate limit exceeded. Please wait and try again.")
                elif 'connection' in error_msg.lower():
                    self.report({'ERROR'}, "Connection error. Please check your internet connection.")
                else:
                    self.report({'ERROR'}, f"API Error: {error_msg}")
                return {'CANCELLED'}

            # Process successful result
            # Check if the result is a question
            if self._result.get('is_question'):
                question = self._result.get('question')
                if question:
                    # Add assistant question to chat history
                    message = context.scene.blendpro_chat_history.add()
                    message.type = 'assistant'
                    message.content = question

                    # Auto-save chat history
                    from .utilities import save_chat_history
                    save_chat_history(context.scene.blendpro_chat_history)

                    # Set waiting state for answer
                    self._waiting_for_answer = True
                    self._current_question = question

                    # Show the question to user
                    self.report({'INFO'}, f"AI Question: {question}")
                    print(f"AI asked: {question}")

                    return {'FINISHED'}

            # Check if it's a plan preview (needs user approval)
            elif self._result.get('is_plan_preview'):
                steps = self._result.get('steps', [])
                plan_preview = self._result.get('plan_preview', '')

                if steps:
                    # Create an interactive message in the chat history
                    message = context.scene.blendpro_chat_history.add()
                    message.type = 'assistant'
                    message.content = f"I've created a {len(steps)}-step plan for this task:\n\n{plan_preview}"
                    message.is_interactive = True  # Mark as interactive
                    message.plan_data = json.dumps(steps)  # Store plan data

                    # Auto-save chat history
                    from .utilities import save_chat_history
                    save_chat_history(context.scene.blendpro_chat_history)

                    # Clear the input field and show success message
                    context.scene.blendpro_chat_input = ""
                    self.report({'INFO'}, f"Plan created with {len(steps)} steps. Please review and choose an action.")
                    print(f"Plan preview:\n{plan_preview}")

                    return {'FINISHED'}

            # Check if it's a multi-step result (already executed)
            elif self._result.get('is_multi_step'):
                blender_code = self._result.get('code')
                steps = self._result.get('steps', [])

                if blender_code and blender_code.strip():
                    # Add assistant response to chat history with step information
                    message = context.scene.blendpro_chat_history.add()
                    message.type = 'assistant'
                    message.content = f"Multi-step task completed ({len(steps)} steps):\n{blender_code}"

                    # Auto-save chat history
                    from .utilities import save_chat_history
                    save_chat_history(context.scene.blendpro_chat_history)

                    # Show code preview
                    bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)

                    # Report success with step count
                    self.report({'INFO'}, f"Multi-step task completed with {len(steps)} steps")
                else:
                    self.report({'ERROR'}, "Multi-step task failed to generate code")
                    return {'CANCELLED'}

            # Regular single-step result
            else:
                blender_code = self._result.get('code')
                if blender_code and blender_code.strip():
                    # Add assistant response to chat history
                    message = context.scene.blendpro_chat_history.add()
                    message.type = 'assistant'
                    message.content = blender_code

                    # Auto-save chat history after adding assistant response
                    from .utilities import save_chat_history
                    save_chat_history(context.scene.blendpro_chat_history)

                    # Show code preview instead of direct execution
                    bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)
                else:
                    # More detailed error reporting
                    error_details = self._result.get('error', 'Unknown error')
                    self.report({'ERROR'}, f"No code generated from API response. Details: {error_details}")
                    print(f"Full API result: {self._result}")  # Debug output
                    return {'CANCELLED'}

            return {'FINISHED'}

        return {'PASS_THROUGH'}

    def _process_result_directly(self, context):
        """Process a result directly without going through the modal handler"""
        if not self._result:
            return

        # Check if it's a multi-step result
        if self._result.get('is_multi_step'):
            blender_code = self._result.get('code')
            steps = self._result.get('steps', [])

            if blender_code and blender_code.strip():
                # Add assistant response to chat history with step information
                message = context.scene.blendpro_chat_history.add()
                message.type = 'assistant'
                message.content = f"Multi-step task completed ({len(steps)} steps):\n{blender_code}"

                # Auto-save chat history
                from .utilities import save_chat_history
                save_chat_history(context.scene.blendpro_chat_history)

                # Show code preview
                bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)

                # Report success with step count
                self.report({'INFO'}, f"Multi-step task completed with {len(steps)} steps")
            else:
                self.report({'ERROR'}, "Multi-step task failed to generate code")


# Vision analysis is now integrated into the main AI assistant automatically

class BLENDPRO_OT_CaptureScreenshot(bpy.types.Operator):
    bl_idname = "blendpro.capture_screenshot"
    bl_label = "Capture Screenshot"
    bl_description = "Capture current viewport screenshot"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not VISION_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "Vision system not available. Please check dependencies.")
            return {'CANCELLED'}

        try:
            from .vision_utilities import capture_viewport_screenshot
            screenshot = capture_viewport_screenshot(context)

            if screenshot:
                self.report({'INFO'}, "Screenshot captured successfully")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to capture screenshot")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Screenshot capture failed: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_TestVisionSystem(bpy.types.Operator):
    bl_idname = "blendpro.test_vision_system"
    bl_label = "Test Vision System"
    bl_description = "Test vision system functionality"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not VISION_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "Vision system not available. Please check dependencies.")
            return {'CANCELLED'}

        try:
            from .vision_utilities import test_vision_system
            test_results = test_vision_system(context)

            # Report results
            status = test_results.get("status", {})
            if status.get("available", False):
                self.report({'INFO'}, "Vision system is fully functional")
            else:
                missing_deps = [k for k, v in status.get("dependencies", {}).items() if not v]
                self.report({'WARNING'}, f"Vision system partially available. Missing: {', '.join(missing_deps)}")

            # Print detailed results to console
            print("Vision System Test Results:")
            print(f"Dependencies: {test_results.get('dependencies', {})}")
            print(f"Tests: {test_results.get('tests', {})}")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Vision system test failed: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ToggleSceneMonitoring(bpy.types.Operator):
    bl_idname = "blendpro.toggle_scene_monitoring"
    bl_label = "Toggle Scene Monitoring"
    bl_description = "Start or stop real-time scene monitoring"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not SCENE_MONITOR_AVAILABLE:
            self.report({'ERROR'}, "Scene monitoring system not available.")
            return {'CANCELLED'}

        try:
            from .scene_monitor import get_monitoring_status, start_scene_monitoring, stop_scene_monitoring

            status = get_monitoring_status()

            if status.get("active", False):
                # Stop monitoring
                success = stop_scene_monitoring()
                if success:
                    self.report({'INFO'}, "Scene monitoring stopped")
                    context.scene.blendpro_monitoring_active = False
                else:
                    self.report({'ERROR'}, "Failed to stop scene monitoring")
            else:
                # Start monitoring
                success = start_scene_monitoring(context)
                if success:
                    self.report({'INFO'}, "Scene monitoring started")
                    context.scene.blendpro_monitoring_active = True
                else:
                    self.report({'ERROR'}, "Failed to start scene monitoring")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Scene monitoring error: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_AnalyzeSceneHealth(bpy.types.Operator):
    bl_idname = "blendpro.analyze_scene_health"
    bl_label = "Analyze Scene Health"
    bl_description = "Perform immediate scene health analysis"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not SCENE_MONITOR_AVAILABLE:
            self.report({'ERROR'}, "Scene monitoring system not available.")
            return {'CANCELLED'}

        try:
            from .scene_monitor import analyze_scene_health_now

            health_report = analyze_scene_health_now(context)

            if health_report.get("error"):
                self.report({'ERROR'}, f"Health analysis failed: {health_report['error']}")
                return {'CANCELLED'}

            # Report results
            overall_score = health_report.get("overall_score", 0)
            issues_count = len(health_report.get("issues", []))
            suggestions_count = len(health_report.get("suggestions", []))

            if overall_score >= 80:
                self.report({'INFO'}, f"Scene health: Excellent ({overall_score:.0f}/100)")
            elif overall_score >= 60:
                self.report({'INFO'}, f"Scene health: Good ({overall_score:.0f}/100)")
            elif overall_score >= 40:
                self.report({'WARNING'}, f"Scene health: Fair ({overall_score:.0f}/100) - {issues_count} issues found")
            else:
                self.report({'ERROR'}, f"Scene health: Poor ({overall_score:.0f}/100) - {issues_count} issues found")

            # Print detailed results to console
            print("\n" + "="*50)
            print("SCENE HEALTH ANALYSIS REPORT")
            print("="*50)
            print(f"Overall Score: {overall_score:.1f}/100")

            if health_report.get("issues"):
                print(f"\nISSUES ({len(health_report['issues'])}):")
                for i, issue in enumerate(health_report["issues"][:5], 1):
                    print(f"  {i}. {issue}")

            if health_report.get("suggestions"):
                print(f"\nSUGGESTIONS ({len(health_report['suggestions'])}):")
                for i, suggestion in enumerate(health_report["suggestions"][:5], 1):
                    print(f"  {i}. {suggestion}")

            if health_report.get("warnings"):
                print(f"\nWARNINGS ({len(health_report['warnings'])}):")
                for i, warning in enumerate(health_report["warnings"][:3], 1):
                    print(f"  {i}. {warning}")

            print("="*50)

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Scene health analysis error: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ShowRecentSuggestions(bpy.types.Operator):
    bl_idname = "blendpro.show_recent_suggestions"
    bl_label = "Show Recent Suggestions"
    bl_description = "Show recent proactive suggestions from scene monitoring"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not SCENE_MONITOR_AVAILABLE:
            self.report({'ERROR'}, "Scene monitoring system not available.")
            return {'CANCELLED'}

        try:
            from .scene_monitor import get_recent_suggestions

            suggestions = get_recent_suggestions()

            if not suggestions:
                self.report({'INFO'}, "No recent suggestions available")
                return {'FINISHED'}

            # Print suggestions to console
            print("\n" + "="*50)
            print("RECENT SCENE SUGGESTIONS")
            print("="*50)
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
            print("="*50)

            self.report({'INFO'}, f"Found {len(suggestions)} recent suggestions (check console)")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error retrieving suggestions: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ApprovePlan(bpy.types.Operator):
    bl_idname = "blendpro.approve_plan"
    bl_label = "Execute Plan"
    bl_options = {'REGISTER'}

    # Property to store the plan steps as JSON
    plan_steps_json: bpy.props.StringProperty()

    def execute(self, context):
        try:
            # Parse the plan steps from JSON
            steps = json.loads(self.plan_steps_json) if self.plan_steps_json else []

            if not steps:
                self.report({'ERROR'}, "No plan data found")
                return {'CANCELLED'}

            # Get API configuration
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            api_key = addon_prefs.api_key or os.getenv("OPENAI_API_KEY")
            if not api_key:
                self.report({'ERROR'}, "No API key found")
                return {'CANCELLED'}

            base_url = addon_prefs.base_url or os.getenv("OPENAI_BASE_URL")
            model = addon_prefs.custom_model if addon_prefs.use_custom_model else context.scene.blendpro_model

            # Add approval message to chat history
            message = context.scene.blendpro_chat_history.add()
            message.type = 'user'
            message.content = "(Plan Approved - Executing...)"

            # Execute the approved plan
            from .utilities import execute_approved_plan, save_chat_history
            result = execute_approved_plan(
                steps,
                context,
                system_prompt,
                api_key,
                base_url,
                model
            )

            # Process the result
            if result.get('error'):
                self.report({'ERROR'}, f"Plan execution failed: {result['error']}")
                return {'CANCELLED'}

            blender_code = result.get('code')
            if blender_code and blender_code.strip():
                # Add assistant response to chat history
                assistant_message = context.scene.blendpro_chat_history.add()
                assistant_message.type = 'assistant'
                assistant_message.content = f"Multi-step plan executed ({len(steps)} steps):\n{blender_code}"

                # Save chat history
                save_chat_history(context.scene.blendpro_chat_history)

                # Show code preview
                bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)

                self.report({'INFO'}, f"Plan executed successfully with {len(steps)} steps")
            else:
                self.report({'ERROR'}, "Plan execution failed to generate code")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Plan execution error: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

class BLENDPRO_OT_RejectPlan(bpy.types.Operator):
    bl_idname = "blendpro.reject_plan"
    bl_label = "Reject Plan (Use Single Step)"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            # Add rejection message to chat history
            message = context.scene.blendpro_chat_history.add()
            message.type = 'user'
            message.content = "(Plan Rejected - Please enter your request again for single-step approach)"

            # Auto-save chat history
            from .utilities import save_chat_history
            save_chat_history(context.scene.blendpro_chat_history)

            # Clear input field and show message
            context.scene.blendpro_chat_input = ""
            self.report({'INFO'}, "Plan rejected. Please enter your request again for single-step processing.")

        except Exception as e:
            self.report({'ERROR'}, f"Error rejecting plan: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

class BLENDPRO_OT_TestConnection(bpy.types.Operator):
    bl_idname = "blendpro.test_connection"
    bl_label = "Test API Connection"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key found")
            return {'CANCELLED'}

        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        model = addon_prefs.custom_model if addon_prefs.use_custom_model else context.scene.blendpro_model

        from .utilities import test_openrouter_connection
        result = test_openrouter_connection(api_key, base_url, model)

        if result["success"]:
            self.report({'INFO'}, f"Connection successful! Response: {result['content'][:50]}...")
        else:
            self.report({'ERROR'}, f"Connection failed: {result['error']}")

        return {'FINISHED'}




def menu_func(self, context):
    self.layout.operator(BLENDPRO_OT_Execute.bl_idname)

class BLENDPROAddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your OpenAI API Key",
        default="",
        subtype="PASSWORD",
    )

    base_url: bpy.props.StringProperty(
        name="Base URL",
        description="OpenAI API Base URL (leave empty for default: https://api.openai.com/v1)",
        default="",
    )

    custom_model: bpy.props.StringProperty(
        name="Custom Model",
        description="Custom model ID (e.g., gpt-4-turbo, claude-3-opus, local-model)",
        default="",
    )

    use_custom_model: bpy.props.BoolProperty(
        name="Use Custom Model",
        description="Use custom model instead of predefined ones",
        default=False,
    )

    # AI Configuration Parameters
    temperature: bpy.props.FloatProperty(
        name="Temperature",
        description="Controls randomness in the output (0.0 = deterministic, 2.0 = very random)",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1,
        precision=1,
    )

    max_tokens: bpy.props.IntProperty(
        name="Max Tokens",
        description="Maximum number of tokens to generate in the response",
        default=1500,
        min=1,
        max=4000,
    )

    top_p: bpy.props.FloatProperty(
        name="Top P",
        description="Controls diversity via nucleus sampling (0.1 = only top 10% of tokens)",
        default=1.0,
        min=0.0,
        max=1.0,
        step=0.1,
        precision=2,
    )

    # Vision System Configuration
    enable_vision_context: bpy.props.BoolProperty(
        name="Enable Vision Context",
        description="Automatically add scene context to AI requests when relevant keywords are detected",
        default=True,
    )

    # Vision API Configuration (separate from base AI)
    vision_api_key: bpy.props.StringProperty(
        name="Vision API Key",
        description="API Key for vision model (leave empty to use main API key)",
        default="",
        subtype="PASSWORD",
    )

    vision_base_url: bpy.props.StringProperty(
        name="Vision Base URL",
        description="Base URL for vision API (leave empty to use main base URL)",
        default="",
    )

    use_custom_vision_model: bpy.props.BoolProperty(
        name="Use Custom Vision Model",
        description="Use custom vision model instead of predefined ones",
        default=False,
    )

    custom_vision_model: bpy.props.StringProperty(
        name="Custom Vision Model",
        description="Custom vision model ID (e.g., gpt-4-vision-preview, claude-3-5-sonnet-20241022)",
        default="",
    )

    auto_vision_keywords: bpy.props.StringProperty(
        name="Auto Vision Keywords",
        description="Comma-separated keywords that trigger automatic vision context",
        default="scene,current,visible,see,look,analyze,what,this,these,objects",
    )

    # Scene Monitoring Configuration
    enable_scene_monitoring: bpy.props.BoolProperty(
        name="Enable Scene Monitoring",
        description="Allow real-time scene monitoring and proactive suggestions",
        default=True,
    )

    monitoring_interval: bpy.props.FloatProperty(
        name="Monitoring Interval",
        description="Time between scene checks in seconds",
        default=2.0,
        min=0.5,
        max=10.0,
    )

    analysis_cooldown: bpy.props.FloatProperty(
        name="Analysis Cooldown",
        description="Minimum time between full scene analyses in seconds",
        default=10.0,
        min=5.0,
        max=60.0,
    )

    max_suggestions: bpy.props.IntProperty(
        name="Max Suggestions",
        description="Maximum number of suggestions to keep in queue",
        default=10,
        min=3,
        max=20,
    )

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "api_key")
        layout.separator()
        layout.prop(self, "base_url")
        layout.separator()
        layout.prop(self, "use_custom_model")
        if self.use_custom_model:
            layout.prop(self, "custom_model")
        layout.separator()

        # AI Configuration Section
        box = layout.box()
        box.label(text="AI Configuration:", icon="SETTINGS")
        box.prop(self, "temperature")
        box.prop(self, "max_tokens")
        box.prop(self, "top_p")

        layout.separator()

        # Smart Context System Configuration
        if VISION_SYSTEM_AVAILABLE or SCENE_MONITOR_AVAILABLE:
            context_box = layout.box()
            context_box.label(text="Smart Context System")

            if VISION_SYSTEM_AVAILABLE:
                # Vision API Configuration (collapsed by default)
                context_box.prop(self, "vision_api_key")
                context_box.prop(self, "vision_base_url")

                # Vision Model Selection
                context_box.prop(self, "use_custom_vision_model")
                if self.use_custom_vision_model:
                    context_box.prop(self, "custom_vision_model")
                else:
                    context_box.prop(context.scene, "blendpro_vision_model", text="Vision Model")

                # Context status
                try:
                    from .vision_utilities import get_vision_system_status
                    vision_status = get_vision_system_status()
                    if vision_status.get("available", False):
                        context_box.label(text="Vision Context: Ready")
                    else:
                        context_box.label(text="Vision Context: Limited")
                except:
                    context_box.label(text="Vision Context: Unknown")
        else:
            context_box = layout.box()
            context_box.label(text="Smart Context System")
            context_box.label(text="Context system not available")
            context_box.label(text="Install: pip install opencv-python pillow numpy")

        layout.separator()

        if SCENE_MONITOR_AVAILABLE:
                # Scene monitoring settings
                context_box.separator()
                context_box.prop(self, "enable_scene_monitoring")
                context_box.prop(self, "monitoring_interval")

                # Monitoring status
                try:
                    from .scene_monitor import get_monitoring_status
                    status = get_monitoring_status()
                    if status.get("active", False):
                        context_box.label(text="Scene Monitoring: Active")
                    else:
                        context_box.label(text="Scene Monitoring: Inactive")
                except:
                    context_box.label(text="Scene Monitoring: Unknown")

        layout.separator()
        layout.operator("blendpro.test_connection", text="Test API Connection")

def register():
    bpy.utils.register_class(BLENDPROAddonPreferences)
    bpy.utils.register_class(BLENDPRO_OT_Execute)
    bpy.utils.register_class(BLENDPRO_PT_Panel)
    bpy.utils.register_class(BLENDPRO_OT_ClearChat)
    bpy.utils.register_class(BLENDPRO_OT_ExportChat)
    bpy.utils.register_class(BLENDPRO_OT_ImportChat)
    bpy.utils.register_class(BLENDPRO_OT_CodePreview)
    bpy.utils.register_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.register_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.register_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.register_class(BLENDPRO_OT_ShowCode)
    bpy.utils.register_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.register_class(BLENDPRO_OT_TestConnection)
    bpy.utils.register_class(BLENDPRO_OT_ApprovePlan)
    bpy.utils.register_class(BLENDPRO_OT_RejectPlan)

    # Register vision system operators if available (minimal set)
    if VISION_SYSTEM_AVAILABLE:
        # Keep only essential vision operators
        bpy.utils.register_class(BLENDPRO_OT_TestVisionSystem)

    # Register scene monitoring operators if available
    if SCENE_MONITOR_AVAILABLE:
        bpy.utils.register_class(BLENDPRO_OT_ToggleSceneMonitoring)
        bpy.utils.register_class(BLENDPRO_OT_AnalyzeSceneHealth)
        bpy.utils.register_class(BLENDPRO_OT_ShowRecentSuggestions)

    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()

    # Initialize smart context system
    print("BlendPro: Smart AI Context System initialized")
    if VISION_SYSTEM_AVAILABLE:
        print("BlendPro: Vision context available")
    if SCENE_MONITOR_AVAILABLE:
        print("BlendPro: Scene monitoring available")

    # Load saved chat history on addon startup
    def load_history_delayed():
        """Load chat history with a small delay to ensure context is ready"""
        try:
            if bpy.context and bpy.context.scene:
                from .utilities import load_chat_history
                load_chat_history(bpy.context)
        except Exception as e:
            print(f"Error loading chat history on startup: {e}")

    # Use a timer to delay loading slightly
    bpy.app.timers.register(load_history_delayed, first_interval=0.1)


def unregister():
    bpy.utils.unregister_class(BLENDPROAddonPreferences)
    bpy.utils.unregister_class(BLENDPRO_OT_Execute)
    bpy.utils.unregister_class(BLENDPRO_PT_Panel)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ExportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ImportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_CodePreview)
    bpy.utils.unregister_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowCode)
    bpy.utils.unregister_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.unregister_class(BLENDPRO_OT_TestConnection)
    bpy.utils.unregister_class(BLENDPRO_OT_ApprovePlan)
    bpy.utils.unregister_class(BLENDPRO_OT_RejectPlan)

    # Unregister vision system operators if they were registered
    if VISION_SYSTEM_AVAILABLE:
        try:
            bpy.utils.unregister_class(BLENDPRO_OT_TestVisionSystem)
        except:
            pass  # Classes might not be registered if vision system failed to load

    # Unregister scene monitoring operators if they were registered
    if SCENE_MONITOR_AVAILABLE:
        try:
            bpy.utils.unregister_class(BLENDPRO_OT_ToggleSceneMonitoring)
            bpy.utils.unregister_class(BLENDPRO_OT_AnalyzeSceneHealth)
            bpy.utils.unregister_class(BLENDPRO_OT_ShowRecentSuggestions)
        except:
            pass  # Classes might not be registered if scene monitor failed to load

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
