# BlendPro İnteraktif Plan Onayı Sistemi

## 🎯 Yeni Özellik: Akıllı Çok Adımlı Görev Planlama

BlendPro artık karmaşık görevleri otomatik olarak algılayıp, bunları adım adım planlara bölebilir ve size onay için sunar. <PERSON><PERSON>ste<PERSON>, daha kontrollü ve şeffaf bir AI deneyimi sağlar.

## 🚀 Nasıl Çalışır?

### 1. Karmaşık Görev Algılama
Sistem şu tür komutları otomatik olarak karmaşık olarak algılar:
- **Çoklu objeler**: "masa ve sandalye", "table and chair"
- **Me<PERSON> oluşturma**: "oda", "room", "sahne", "scene"
- **Malzeme + obje**: "ahşap masa", "wooden table"
- **Konumsal ilişkiler**: "yanında", "içinde", "next to", "inside"

### 2. Plan Oluşturma
Karmaşık görev algılandığında:
- AI görevi 5-15 adıma böler
- Her adım tek bir spesifik işlem içerir
- Plan mantıklı sırayla düzenlenir

### 3. İnteraktif Onay
Plan oluşturulduktan sonra sohbet geçmişinde özel bir mesaj görünür:

```
🤖 Assistant's Plan:
I've created a 7-step plan for this task:

1. Create a cube for the table top
2. Scale and position the table
3. Create wooden material
4. Apply material to table
5. Create chair object
6. Position chair next to table
7. Apply wooden material to chair

[✓ Execute Plan]  [✗ Single Step]  [🗑️]
```

## 🎮 Kullanım Örnekleri

### Örnek 1: Türkçe Komut
**Komut**: "Ahşap bir masa ve yanında duran bir sandalye yap"

**Sonuç**: 
- Sistem karmaşık görev algılar
- 6-8 adımlık plan oluşturur
- İnteraktif onay mesajı gösterir

### Örnek 2: İngilizce Komut
**Komut**: "Create a room with furniture inside"

**Sonuç**:
- Sistem karmaşık görev algılar
- 10-15 adımlık plan oluşturur
- İnteraktif onay mesajı gösterir

### Örnek 3: Basit Komut
**Komut**: "Bir küp oluştur"

**Sonuç**:
- Sistem basit görev algılar
- Doğrudan tek adımda çalıştırır
- Plan oluşturmaz

## 🔘 Buton Açıklamaları

### ✓ Execute Plan (Planı Çalıştır)
- Oluşturulan planı adım adım çalıştırır
- Her adım için ayrı AI çağrısı yapar
- Adımlar arası bağlam korunur
- Sonunda birleştirilmiş kod gösterir

### ✗ Single Step (Tek Adım)
- Planı reddeder
- Kullanıcıdan komutu tekrar girmesini ister
- Tek adımda işlemeye çalışır

### 🗑️ Delete (Sil)
- Mesajı sohbet geçmişinden siler
- Plan iptal edilir

## ⚡ Avantajlar

### 1. Kontrol
- Ne yapılacağını önceden görürsünüz
- İstemediğiniz planları reddedebilirsiniz
- Şeffaf süreç

### 2. Kalite
- Karmaşık görevler daha iyi sonuç verir
- Adım adım yaklaşım daha az hata
- Bağlamsal süreklilik

### 3. Esneklik
- Plan beğenmezseniz tek adım seçebilirsiniz
- Her zaman kontrol sizde
- Kolay iptal etme

## 🛠️ Teknik Detaylar

### Karmaşık Görev Göstergeleri
Sistem şu kelimeleri arar:
- **Bağlaçlar**: and, ve, ile, sonra, then, after
- **Mekan**: oda, room, sahne, scene, ortam, environment
- **Mobilya**: masa, table, sandalye, chair, dolap, cabinet
- **Malzeme**: ahşap, wood, metal, cam, glass, taş, stone
- **Konum**: içinde, inside, üzerinde, on top, yanında, next to
- **Boyut**: büyük, küçük, yüksek, alçak, large, small, tall, short

### Plan Oluşturma Süreci
1. Prompt analiz edilir
2. AI'dan JSON formatında adım listesi istenir
3. Her adım tek bir işlem içerir
4. Mantıklı sıralama kontrol edilir
5. Kullanıcıya sunulur

### Çalıştırma Süreci
1. Kullanıcı planı onaylar
2. Her adım sırayla AI'ya gönderilir
3. Önceki adımların bağlamı korunur
4. Oluşturulan objeler takip edilir
5. Sonuçlar birleştirilir

## 🔧 Sorun Giderme

### Plan Oluşturulmuyor
- API bağlantınızı kontrol edin
- Komutunuz yeterince karmaşık olmayabilir
- Konsol çıktısını kontrol edin

### Butonlar Görünmüyor
- Blender'ı yeniden başlatın
- Eklenti güncellemelerini kontrol edin
- Panel'i yenileyin

### Plan Çalıştırılmıyor
- API key'inizi kontrol edin
- İnternet bağlantınızı kontrol edin
- Konsol hatalarını kontrol edin

## 📝 İpuçları

1. **Açık komutlar verin**: "Masa ve sandalye yap" yerine "Ahşap bir masa ve yanında duran bir sandalye yap"
2. **Planı inceleyin**: Çalıştırmadan önce adımları okuyun
3. **Tek adım deneyin**: Plan beğenmezseniz tek adım seçeneğini kullanın
4. **Konsolu takip edin**: Detaylı bilgi için konsol çıktısını izleyin

## 🎉 Sonuç

Bu yeni sistem, BlendPro'yu çok daha güçlü ve kontrollü hale getiriyor. Artık karmaşık sahneler oluştururken tam olarak ne olacağını biliyorsunuz ve süreci kontrol edebiliyorsunuz.

**Keyifli modelleme!** 🎨
